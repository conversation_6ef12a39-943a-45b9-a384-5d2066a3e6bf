import React from 'react';
import ForgotPasswordForm from '../../components/auth/ForgotPasswordForm';
import { useAuth } from '../../context/AuthContext';
import './css/ForgotPasswordPage.css';

const ForgotPasswordPage = () => {
  const { forgotPassword } = useAuth();

  const handleForgotPassword = async (email) => {
    try {
      await forgotPassword(email);
      // Success is handled in the form component
    } catch (err) {
      console.error('Forgot password error:', err);
    }
  };

  return (
    <div className="page-container">
      <div className="left-section">
        <img className="logo" src="/assets/images/logos/noble-logo.svg" alt="NOBLE Platform" />

        <h1 className="hero-title">Build Smarter, Manage Faster:</h1>
        <p className="hero-subtitle">
          Your Ultimate Construction Project Management Solution
        </p>

        <a className="learn-more-button" href="#">Learn More</a>
      </div>

      <div className="right-section">
        <img className="company-logo" src="/assets/images/logos/stickbuild.svg" alt="Stick Build P/L" />

        <ForgotPasswordForm onSubmit={handleForgotPassword} />
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
