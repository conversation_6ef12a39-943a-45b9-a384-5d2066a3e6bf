import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import Input from '../common/Input';
import Button from '../common/Button';
import ColorPalette from '../../styles/ColorPalette';
import Typography from '../../styles/Typography';

const ForgotPasswordContainer = styled.div`
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  background-color: ${ColorPalette.background.paper};
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const FormTitle = styled.h3`
  font-family: ${Typography.fontFamily.secondary};
  font-size: ${Typography.fontSize['2xl']};
  font-weight: ${Typography.fontWeight.semiBold};
  color: ${ColorPalette.text.primary};
  margin-bottom: 0.5rem;
  text-align: center;
`;

const FormSubtitle = styled.p`
  font-size: ${Typography.fontSize.md};
  color: ${ColorPalette.text.secondary};
  margin-bottom: 2rem;
  text-align: center;
`;

const BackToLoginLink = styled(Link)`
  font-size: ${Typography.fontSize.sm};
  color: ${ColorPalette.primary.main};
  display: block;
  text-align: center;
  margin-top: 1.5rem;

  &:hover {
    text-decoration: underline;
  }
`;

const SuccessMessage = styled.div`
  background-color: ${ColorPalette.status.success}20;
  color: ${ColorPalette.status.success};
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: ${Typography.fontSize.sm};
`;

const ForgotPasswordForm = ({ onSubmit }) => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    setEmail(e.target.value);
    setError('');
  };

  const validateForm = () => {
    if (!email.trim()) {
      setError('Email is required');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      setIsLoading(true);
      try {
        await onSubmit?.(email);
        setIsSubmitted(true);
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to send password reset email.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <ForgotPasswordContainer>
      <FormTitle>Forgot Password</FormTitle>
      <FormSubtitle>
        Enter your email address and we'll send you a link to reset your password
      </FormSubtitle>

      {isSubmitted ? (
        <>
          <SuccessMessage>
            Password reset instructions have been sent to your email address.
            Please check your inbox and follow the instructions to reset your password.
          </SuccessMessage>
          <BackToLoginLink to="/login">Back to Login</BackToLoginLink>
        </>
      ) : (
        <>
          <form onSubmit={handleSubmit}>
            <Input
              label="Email Address"
              id="email"
              name="email"
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={handleChange}
              error={error}
            />

            <Button type="submit" fullWidth disabled={isLoading}>
              {isLoading ? 'Sending...' : 'Send Reset Link'}
            </Button>
          </form>

          <BackToLoginLink to="/login">Back to Login</BackToLoginLink>
        </>
      )}
    </ForgotPasswordContainer>
  );
};

export default ForgotPasswordForm;
